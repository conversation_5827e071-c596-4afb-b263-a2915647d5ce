import { existsSync, readFileSync, writeFileSync, mkdirSync, readdirSync } from 'fs';
import { join, dirname } from 'path';

// Enum definitions for UI display
export const ConditionOpEnum = {
    0: 'And',
    1: 'Or'
};

export const CompareOpEnum = {
    0: '==',
    1: '!=',
    2: '>',
    3: '<',
    4: '>=',
    5: '<='
};

// These should match your actual game enums defined in EventConditionType.ts
export const EmitterConditionTypeEnum = {
    0: 'Level_Duration',
    1: 'Level_Distance',
    2: 'Level_InfLevel',
    3: 'Level_ChallengeLevel',
    4: 'Player_ActLevel',
    5: 'Player_PosX',
    6: 'Player_PosY',
    7: 'Player_LifePercent',
    8: 'Player_GainBuff',
    9: 'Unit_Life',
    10: 'Unit_LifePercent',
    11: 'Unit_Duration',
    12: 'Unit_PosX',
    13: 'Unit_PosY',
    14: 'Unit_Speed',
    15: 'Unit_SpeedAngle',
    16: 'Unit_Acceleration',
    17: 'Unit_AccelerationAngle',
    18: 'Unit_DistanceToPlayer',
    19: 'Unit_AngleToPlayer',
    20: 'Emitter_Active',
    21: 'Emitter_InitialDelay',
    22: 'Emitter_Prewarm',
    23: 'Emitter_PrewarmDuration',
    24: 'Emitter_Duration',
    25: 'Emitter_ElapsedTime',
    26: 'Emitter_Loop',
    27: 'Emitter_LoopInterval',
    28: 'Emitter_EmitInterval',
    29: 'Emitter_EmitCount',
    30: 'Emitter_EmitOffsetX',
    31: 'Emitter_Angle',
    32: 'Emitter_Count',
    33: 'Bullet_Sprite',
    34: 'Bullet_Scale',
    35: 'Bullet_ColorR',
    36: 'Bullet_ColorG',
    37: 'Bullet_ColorB',
    38: 'Bullet_Duration',
    39: 'Bullet_ElapsedTime',
    40: 'Bullet_Speed',
    41: 'Bullet_Acceleration',
    42: 'Bullet_AccelerationAngle',
    43: 'Bullet_FacingMoveDir',
    44: 'Bullet_Destructive',
    45: 'Bullet_DestructiveOnHit',
};

export const BulletConditionTypeEnum = {
    0: 'Bullet_Duration',
    1: 'Bullet_ElapsedTime',
    2: 'Bullet_PosX',
    3: 'Bullet_PosY',
    4: 'Bullet_Damage',
    5: 'Bullet_Speed',
    6: 'Bullet_SpeedAngle',
    7: 'Bullet_Acceleration',
    8: 'Bullet_AccelerationAngle',
    9: 'Bullet_Scale',
    10: 'Bullet_ColorR',
    11: 'Bullet_ColorG',
    12: 'Bullet_ColorB',
    13: 'Bullet_FacingMoveDir',
    14: 'Bullet_Destructive',
    15: 'Bullet_DestructiveOnHit',
}

export const EmitterActionTypeEnum = {
    0: 'Emitter_Active',
    1: 'Emitter_InitialDelay',
    2: 'Emitter_Prewarm',
    3: 'Emitter_PrewarmDuration',
    4: 'Emitter_Duration',
    5: 'Emitter_ElapsedTime',
    6: 'Emitter_Loop',
    7: 'Emitter_LoopInterval',
    8: 'Emitter_PerEmitInterval',
    9: 'Emitter_PerEmitCount',
    10: 'Emitter_PerEmitOffsetX',
    11: 'Emitter_Angle',
    12: 'Emitter_Count',
    13: 'Bullet_Duration',
    14: 'Bullet_ElapsedTime',
    15: 'Bullet_PosX',
    16: 'Bullet_PosY',
    17: 'Bullet_Damage',
    18: 'Bullet_Speed',
    19: 'Bullet_SpeedAngle',
    20: 'Bullet_Acceleration',
    21: 'Bullet_AccelerationAngle',
    22: 'Bullet_Scale',
    23: 'Bullet_ColorR',
    24: 'Bullet_ColorG',
    25: 'Bullet_ColorB',
    26: 'Bullet_ColorA',
    27: 'Bullet_FaceMovingDir',
    28: 'Bullet_TrackingTarget',
    29: 'Bullet_Destructive',
    30: 'Bullet_DestructiveOnHit',
    31: 'Unit_Life',
    32: 'Unit_LifePercent',
    33: 'Unit_PosX',
    34: 'Unit_PosY',
    35: 'Unit_Speed',
    36: 'Unit_SpeedAngle',
    37: 'Unit_Acceleration',
    38: 'Unit_AccelerationAngle',
};

export const BulletActionTypeEnum = {
    0: 'Bullet_Duration',
    1: 'Bullet_ElapsedTime',
    2: 'Bullet_PosX',
    3: 'Bullet_PosY',
    4: 'Bullet_Damage',
    5: 'Bullet_Speed',
    6: 'Bullet_SpeedAngle',
    7: 'Bullet_Acceleration',
    8: 'Bullet_AccelerationAngle',
    9: 'Bullet_Scale',
    10: 'Bullet_ColorR',
    11: 'Bullet_ColorG',
    12: 'Bullet_ColorB',
    13: 'Bullet_ColorA',
    14: 'Bullet_FaceMovingDir',
    15: 'Bullet_TrackingTarget',
    16: 'Bullet_Destructive',
    17: 'Bullet_DestructiveOnHit',
};

export const EasingEnum = {
    0: 'Linear',
    1: 'EaseIn',
    2: 'EaseOut',
    3: 'EaseInOut',
    4: 'Bounce'
};

// EventGroupData interfaces (matching the game code structure)
export interface EventConditionData {
    op: number; // eConditionOp
    type: number; // eEventConditionType
    compareOp: number; // eCompareOp
    targetValue: number;
}

export interface EventActionData {
    type: number; // eEventActionType
    duration: number;
    targetValue: number;
    easing: number; // eEasing
}

export interface EventGroupData {
    name: string;
    triggerCount: number;
    conditions: EventConditionData[];
    actions: EventActionData[];
}

export enum EventGroupCategory {
    Emitter = 'Emitter',
    Bullet = 'Bullet',
}

export class EventGroupDataManager {
    private static instance: EventGroupDataManager;
    private projectPath: string;
    private eventsBasePath: string;

    private constructor() {
        this.projectPath = Editor.Project.path;
        this.eventsBasePath = join(this.projectPath, 'assets', 'resources', 'Game', 'emitter', 'events');
    }

    public static getInstance(): EventGroupDataManager {
        if (!EventGroupDataManager.instance) {
            EventGroupDataManager.instance = new EventGroupDataManager();
        }
        return EventGroupDataManager.instance;
    }

    /**
     * Get the full path for a category folder
     */
    private getCategoryPath(category: EventGroupCategory): string {
        return join(this.eventsBasePath, category);
    }

    /**
     * Get the full file path for an EventGroupData
     */
    private getFilePath(category: EventGroupCategory, name: string): string {
        return join(this.getCategoryPath(category), `${name}.json`);
    }

    /**
     * Ensure directory exists
     */
    private ensureDirectoryExists(dirPath: string): void {
        if (!existsSync(dirPath)) {
            mkdirSync(dirPath, { recursive: true });
        }
    }

    /**
     * Load all EventGroupData files from a category
     */
    public loadEventGroupsByCategory(category: EventGroupCategory): EventGroupData[] {
        const categoryPath = this.getCategoryPath(category);
        
        if (!existsSync(categoryPath)) {
            return [];
        }

        const files = readdirSync(categoryPath).filter(file => file.endsWith('.json'));
        const eventGroups: EventGroupData[] = [];

        for (const file of files) {
            try {
                const filePath = join(categoryPath, file);
                const content = readFileSync(filePath, 'utf-8');
                const data = JSON.parse(content) as EventGroupData;
                
                // Ensure the name matches the filename
                data.name = file.replace('.json', '');
                eventGroups.push(data);
            } catch (error) {
                console.error(`Failed to load EventGroupData from ${file}:`, error);
            }
        }

        return eventGroups;
    }

    /**
     * Load a specific EventGroupData by name and category
     */
    public loadEventGroup(category: EventGroupCategory, name: string): EventGroupData | null {
        const filePath = this.getFilePath(category, name);
        
        if (!existsSync(filePath)) {
            return null;
        }

        try {
            const content = readFileSync(filePath, 'utf-8');
            const data = JSON.parse(content) as EventGroupData;
            data.name = name; // Ensure name is correct
            return data;
        } catch (error) {
            console.error(`Failed to load EventGroupData ${name}:`, error);
            return null;
        }
    }

    /**
     * Save an EventGroupData to file
     */
    public saveEventGroup(category: EventGroupCategory, eventGroup: EventGroupData): boolean {
        try {
            const categoryPath = this.getCategoryPath(category);
            this.ensureDirectoryExists(categoryPath);
            
            const filePath = this.getFilePath(category, eventGroup.name);
            const content = JSON.stringify(eventGroup, null, 2);
            
            writeFileSync(filePath, content, 'utf-8');
            return true;
        } catch (error) {
            console.error(`Failed to save EventGroupData ${eventGroup.name}:`, error);
            return false;
        }
    }

    /**
     * Delete an EventGroupData file
     */
    public deleteEventGroup(category: EventGroupCategory, name: string): boolean {
        try {
            const filePath = this.getFilePath(category, name);
            
            if (existsSync(filePath)) {
                const fs = require('fs');
                fs.unlinkSync(filePath);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`Failed to delete EventGroupData ${name}:`, error);
            return false;
        }
    }

    /**
     * Check if an EventGroupData file exists
     */
    public eventGroupExists(category: EventGroupCategory, name: string): boolean {
        const filePath = this.getFilePath(category, name);
        return existsSync(filePath);
    }

    /**
     * Find an EventGroupData by name across all categories
     */
    public findEventGroup(name: string): { category: EventGroupCategory; data: EventGroupData } | null {
        for (const category of Object.values(EventGroupCategory)) {
            const data = this.loadEventGroup(category, name);
            if (data) {
                return { category, data };
            }
        }
        return null;
    }

    /**
     * Generate a unique name for a new EventGroupData
     */
    public generateUniqueName(category: EventGroupCategory, baseName: string = 'EventGroup'): string {
        let counter = 1;
        let name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        
        while (this.eventGroupExists(category, name)) {
            counter++;
            name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        }
        
        return name;
    }

    /**
     * Create a new EventGroupData with default values
     */
    public createNewEventGroup(category: EventGroupCategory, name?: string): EventGroupData {
        const finalName = name || this.generateUniqueName(category);

        var eventGroup: EventGroupData = {
            name: finalName,
            triggerCount: 1,
            conditions: [],
            actions: []
        };

        return eventGroup;
    }

    /**
     * Duplicate an existing EventGroupData
     */
    public duplicateEventGroup(category: EventGroupCategory, originalName: string, newName?: string): EventGroupData | null {
        const original = this.loadEventGroup(category, originalName);
        if (!original) {
            return null;
        }

        const finalName = newName || this.generateUniqueName(category, originalName);
        
        // Deep copy the original data
        const duplicate: EventGroupData = {
            name: finalName,
            triggerCount: original.triggerCount,
            conditions: original.conditions.map(condition => ({ ...condition })),
            actions: original.actions.map(action => ({ ...action }))
        };

        return duplicate;
    }

    /**
     * Get all EventGroupData names from all categories
     */
    public getAllEventGroupNames(): { [category: string]: string[] } {
        const result: { [category: string]: string[] } = {};
        
        for (const category of Object.values(EventGroupCategory)) {
            const eventGroups = this.loadEventGroupsByCategory(category);
            result[category] = eventGroups.map(eg => eg.name);
        }
        
        return result;
    }
}
