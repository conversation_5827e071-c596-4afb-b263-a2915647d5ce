{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EventActionType.ts"], "names": ["_decorator", "ccclass", "property", "eEmitterActionType", "eBulletActionType"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U;AAE9B;AACA;AACA;AACA;;oCACYG,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB;;AA8CZ;AACA;AACA;AACA;;;mCACYC,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB", "sourcesContent": ["import { _decorator, error, v2, Vec2, Prefab, Enum } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * ActionType对应要修改的属性\r\n * 以下是发射器的行为\r\n */\r\nexport enum eEmitterActionType {\r\n    Emitter_Active = 1,         // 发射器是否启用\r\n    Emitter_InitialDelay,       // 发射器当前的初始延迟\r\n    Emitter_Prewarm,            // 发射器是否启用预热\r\n    Emitter_PrewarmDuration,    // 发射器预热的持续时间\r\n    Emitter_Duration,           // 发射器配置的持续时间\r\n    Emitter_ElapsedTime,        // 发射器已运行的时间\r\n    Emitter_Loop,               // 发射器是否循环\r\n    Emitter_LoopInterval,       // 发射器循环的间隔时间\r\n\r\n    Emitter_PerEmitInterval,   // 发射器开火间隔\r\n    Emitter_PerEmitCount,      // 发射器开火次数\r\n    Emitter_PerEmitOffsetX,    // 发射器开火偏移\r\n\r\n    Emitter_Angle,             // 发射器弹道角度\r\n    Emitter_Count,             // 发射器弹道数量\r\n\r\n    Bullet_Duration,\r\n    Bullet_ElapsedTime,\r\n    Bullet_PosX,\r\n    Bullet_PosY,\r\n    Bullet_Damage,\r\n    Bullet_Speed,\r\n    Bullet_SpeedAngle,\r\n    Bullet_Acceleration,\r\n    Bullet_AccelerationAngle,\r\n    Bullet_Scale,\r\n    Bullet_ColorR,\r\n    Bullet_ColorG,\r\n    Bullet_ColorB,\r\n    Bullet_ColorA,\r\n    Bullet_FaceMovingDir,\r\n    Bullet_TrackingTarget,\r\n    Bullet_Destructive,\r\n    Bullet_DestructiveOnHit,\r\n    \r\n    Unit_Life,\r\n    Unit_LifePercent,\r\n    Unit_PosX,\r\n    Unit_PosY,\r\n    Unit_Speed,\r\n    Unit_SpeedAngle,\r\n    Unit_Acceleration,\r\n    Unit_AccelerationAngle,\r\n}\r\n\r\n/**\r\n * ActionType对应要修改的属性\r\n * 以下是子弹的行为\r\n */\r\nexport enum eBulletActionType {\r\n    Bullet_Duration = 100,\r\n    Bullet_ElapsedTime,\r\n    Bullet_PosX,\r\n    Bullet_PosY,\r\n    Bullet_Damage,\r\n    Bullet_Speed,\r\n    Bullet_SpeedAngle,\r\n    Bullet_Acceleration,\r\n    Bullet_AccelerationAngle,\r\n    Bullet_Scale,\r\n    Bullet_ColorR,\r\n    Bullet_ColorG,\r\n    Bullet_ColorB,\r\n    Bullet_ColorA,\r\n    Bullet_FaceMovingDir,\r\n    Bullet_TrackingTarget,\r\n    Bullet_Destructive,\r\n    Bullet_DestructiveOnHit,\r\n}\r\n\r\nexport type eEventActionType = eEmitterActionType | eBulletActionType;\r\n"]}