"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventGroupDataManager = exports.EventGroupCategory = exports.EasingEnum = exports.EventActionTypeEnum = exports.EventConditionTypeEnum = exports.CompareOpEnum = exports.ConditionOpEnum = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
// Enum definitions for UI display
exports.ConditionOpEnum = {
    0: 'And',
    1: 'Or'
};
exports.CompareOpEnum = {
    0: '==',
    1: '!=',
    2: '>',
    3: '<',
    4: '>=',
    5: '<='
};
// These should match your actual game enums
exports.EventConditionTypeEnum = {
    0: 'Level_Duration',
    1: 'Distance',
    2: 'Speed',
    3: 'Custom_1',
    4: 'Custom_2'
};
exports.EventActionTypeEnum = {
    0: 'Emitter_Active',
    1: 'Change_Speed',
    2: 'Change_Direction',
    3: 'Destroy',
    4: 'Custom_Action'
};
exports.EasingEnum = {
    0: 'Linear',
    1: 'EaseIn',
    2: 'EaseOut',
    3: 'EaseInOut',
    4: 'Bounce'
};
var EventGroupCategory;
(function (EventGroupCategory) {
    EventGroupCategory["Emitter"] = "Emitter";
    EventGroupCategory["Bullet"] = "Bullet";
    EventGroupCategory["Others"] = "Others";
})(EventGroupCategory || (exports.EventGroupCategory = EventGroupCategory = {}));
class EventGroupDataManager {
    constructor() {
        this.projectPath = Editor.Project.path;
        this.eventsBasePath = (0, path_1.join)(this.projectPath, 'assets', 'resources', 'Game', 'emitter', 'events');
    }
    static getInstance() {
        if (!EventGroupDataManager.instance) {
            EventGroupDataManager.instance = new EventGroupDataManager();
        }
        return EventGroupDataManager.instance;
    }
    /**
     * Get the full path for a category folder
     */
    getCategoryPath(category) {
        return (0, path_1.join)(this.eventsBasePath, category);
    }
    /**
     * Get the full file path for an EventGroupData
     */
    getFilePath(category, name) {
        return (0, path_1.join)(this.getCategoryPath(category), `${name}.json`);
    }
    /**
     * Ensure directory exists
     */
    ensureDirectoryExists(dirPath) {
        if (!(0, fs_1.existsSync)(dirPath)) {
            (0, fs_1.mkdirSync)(dirPath, { recursive: true });
        }
    }
    /**
     * Load all EventGroupData files from a category
     */
    loadEventGroupsByCategory(category) {
        const categoryPath = this.getCategoryPath(category);
        if (!(0, fs_1.existsSync)(categoryPath)) {
            return [];
        }
        const files = (0, fs_1.readdirSync)(categoryPath).filter(file => file.endsWith('.json'));
        const eventGroups = [];
        for (const file of files) {
            try {
                const filePath = (0, path_1.join)(categoryPath, file);
                const content = (0, fs_1.readFileSync)(filePath, 'utf-8');
                const data = JSON.parse(content);
                // Ensure the name matches the filename
                data.name = file.replace('.json', '');
                eventGroups.push(data);
            }
            catch (error) {
                console.error(`Failed to load EventGroupData from ${file}:`, error);
            }
        }
        return eventGroups;
    }
    /**
     * Load a specific EventGroupData by name and category
     */
    loadEventGroup(category, name) {
        const filePath = this.getFilePath(category, name);
        if (!(0, fs_1.existsSync)(filePath)) {
            return null;
        }
        try {
            const content = (0, fs_1.readFileSync)(filePath, 'utf-8');
            const data = JSON.parse(content);
            data.name = name; // Ensure name is correct
            return data;
        }
        catch (error) {
            console.error(`Failed to load EventGroupData ${name}:`, error);
            return null;
        }
    }
    /**
     * Save an EventGroupData to file
     */
    saveEventGroup(category, eventGroup) {
        try {
            const categoryPath = this.getCategoryPath(category);
            this.ensureDirectoryExists(categoryPath);
            const filePath = this.getFilePath(category, eventGroup.name);
            const content = JSON.stringify(eventGroup, null, 2);
            (0, fs_1.writeFileSync)(filePath, content, 'utf-8');
            return true;
        }
        catch (error) {
            console.error(`Failed to save EventGroupData ${eventGroup.name}:`, error);
            return false;
        }
    }
    /**
     * Delete an EventGroupData file
     */
    deleteEventGroup(category, name) {
        try {
            const filePath = this.getFilePath(category, name);
            if ((0, fs_1.existsSync)(filePath)) {
                const fs = require('fs');
                fs.unlinkSync(filePath);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error(`Failed to delete EventGroupData ${name}:`, error);
            return false;
        }
    }
    /**
     * Check if an EventGroupData file exists
     */
    eventGroupExists(category, name) {
        const filePath = this.getFilePath(category, name);
        return (0, fs_1.existsSync)(filePath);
    }
    /**
     * Find an EventGroupData by name across all categories
     */
    findEventGroup(name) {
        for (const category of Object.values(EventGroupCategory)) {
            const data = this.loadEventGroup(category, name);
            if (data) {
                return { category, data };
            }
        }
        return null;
    }
    /**
     * Generate a unique name for a new EventGroupData
     */
    generateUniqueName(category, baseName = 'EventGroup') {
        let counter = 1;
        let name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        while (this.eventGroupExists(category, name)) {
            counter++;
            name = `${baseName}_${counter.toString().padStart(3, '0')}`;
        }
        return name;
    }
    /**
     * Create a new EventGroupData with default values
     */
    createNewEventGroup(category, name) {
        const finalName = name || this.generateUniqueName(category);
        var eventGroup = {
            name: finalName,
            triggerCount: 1,
            conditions: [],
            actions: []
        };
        return eventGroup;
    }
    /**
     * Duplicate an existing EventGroupData
     */
    duplicateEventGroup(category, originalName, newName) {
        const original = this.loadEventGroup(category, originalName);
        if (!original) {
            return null;
        }
        const finalName = newName || this.generateUniqueName(category, originalName);
        // Deep copy the original data
        const duplicate = {
            name: finalName,
            triggerCount: original.triggerCount,
            conditions: original.conditions.map(condition => (Object.assign({}, condition))),
            actions: original.actions.map(action => (Object.assign({}, action)))
        };
        return duplicate;
    }
    /**
     * Get all EventGroupData names from all categories
     */
    getAllEventGroupNames() {
        const result = {};
        for (const category of Object.values(EventGroupCategory)) {
            const eventGroups = this.loadEventGroupsByCategory(category);
            result[category] = eventGroups.map(eg => eg.name);
        }
        return result;
    }
}
exports.EventGroupDataManager = EventGroupDataManager;
//# sourceMappingURL=data:application/json;base64,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